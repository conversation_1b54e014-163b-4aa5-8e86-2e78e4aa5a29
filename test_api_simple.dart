import 'dart:convert';
import 'dart:io';

// 简单的API测试脚本
void main() async {
  final client = HttpClient();
  final baseUrl = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';
  
  print('🚀 开始测试CloudBase API...');
  print('API地址: $baseUrl');
  print('=' * 50);
  
  try {
    // 测试1: 基础连接
    print('1. 测试基础连接...');
    await testBasicConnection(client, baseUrl);
    
    // 测试2: 登录接口
    print('\n2. 测试登录接口...');
    await testLogin(client, baseUrl);
    
    // 测试3: 获取套餐
    print('\n3. 测试获取套餐...');
    await testPackages(client, baseUrl);
    
    // 测试4: 发送验证码
    print('\n4. 测试发送验证码...');
    await testSendCode(client, baseUrl);
    
    print('\n' + '=' * 50);
    print('🎉 所有API测试通过！CloudBase部署成功！');
    
  } catch (e) {
    print('\n❌ API测试失败: $e');
  } finally {
    client.close();
  }
}

Future<void> testBasicConnection(HttpClient client, String baseUrl) async {
  final request = await client.getUrl(Uri.parse(baseUrl));
  final response = await request.close();
  final responseBody = await response.transform(utf8.decoder).join();
  
  if (response.statusCode == 200) {
    final data = jsonDecode(responseBody);
    print('✅ 基础连接成功');
    print('   响应: ${data['message']}');
  } else {
    throw Exception('基础连接失败: ${response.statusCode}');
  }
}

Future<void> testLogin(HttpClient client, String baseUrl) async {
  final request = await client.postUrl(Uri.parse('$baseUrl/auth/login'));
  request.headers.contentType = ContentType.json;
  
  final loginData = {
    'username': 'testuser',
    'password': 'ecd71870d1963316a97e3ac3408c9835ad8cf0f3c1bc703527c30265534f75ae'
  };
  
  request.add(utf8.encode(jsonEncode(loginData)));
  final response = await request.close();
  final responseBody = await response.transform(utf8.decoder).join();
  
  if (response.statusCode == 200) {
    final data = jsonDecode(responseBody);
    print('✅ 登录成功');
    print('   用户: ${data['data']['user']['username']}');
    final token = data['data']['token'].toString();
    print('   Token: ${token.length > 20 ? token.substring(0, 20) + '...' : token}');
  } else {
    throw Exception('登录失败: ${response.statusCode} - $responseBody');
  }
}

Future<void> testPackages(HttpClient client, String baseUrl) async {
  final request = await client.getUrl(Uri.parse('$baseUrl/packages'));
  final response = await request.close();
  final responseBody = await response.transform(utf8.decoder).join();
  
  if (response.statusCode == 200) {
    final data = jsonDecode(responseBody);
    print('✅ 获取套餐成功');
    print('   套餐数量: ${data['data'].length}');
    if (data['data'].isNotEmpty) {
      print('   第一个套餐: ${data['data'][0]['name']} - ¥${data['data'][0]['price']}');
    }
  } else {
    throw Exception('获取套餐失败: ${response.statusCode}');
  }
}

Future<void> testSendCode(HttpClient client, String baseUrl) async {
  final request = await client.postUrl(Uri.parse('$baseUrl/auth/send-code'));
  request.headers.contentType = ContentType.json;
  
  final codeData = {
    'phoneNumber': '13800138000'
  };
  
  request.add(utf8.encode(jsonEncode(codeData)));
  final response = await request.close();
  final responseBody = await response.transform(utf8.decoder).join();
  
  if (response.statusCode == 200) {
    final data = jsonDecode(responseBody);
    print('✅ 发送验证码成功');
    print('   消息: ${data['message']}');
  } else {
    throw Exception('发送验证码失败: ${response.statusCode}');
  }
}
