import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';

// 测试Flutter应用的登录逻辑
void main() async {
  print('🧪 测试Flutter登录逻辑...');
  print('=' * 50);
  
  // 测试密码哈希
  testPasswordHashing();
  
  // 测试API调用
  await testApiCall();
}

void testPasswordHashing() {
  print('1. 测试密码哈希...');
  
  final testPasswords = ['test123', 'admin123', 'password', '123456'];
  
  for (final password in testPasswords) {
    final hash = _hashPassword(password);
    print('   密码: $password');
    print('   哈希: $hash');
    print('');
  }
}

Future<void> testApiCall() async {
  print('2. 测试API调用...');
  
  final client = HttpClient();
  final baseUrl = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';
  
  try {
    // 测试testuser登录
    print('   测试用户: testuser, 密码: test123');
    final success1 = await _testLogin(client, baseUrl, 'testuser', 'test123');
    print('   结果: ${success1 ? "✅ 成功" : "❌ 失败"}');
    
    // 测试admin登录
    print('   测试用户: admin, 密码: admin123');
    final success2 = await _testLogin(client, baseUrl, 'admin', 'admin123');
    print('   结果: ${success2 ? "✅ 成功" : "❌ 失败"}');
    
    // 测试错误密码
    print('   测试用户: testuser, 密码: wrongpassword');
    final success3 = await _testLogin(client, baseUrl, 'testuser', 'wrongpassword');
    print('   结果: ${success3 ? "✅ 成功" : "❌ 失败（预期）"}');
    
  } catch (e) {
    print('   ❌ 测试失败: $e');
  } finally {
    client.close();
  }
}

Future<bool> _testLogin(HttpClient client, String baseUrl, String username, String password) async {
  try {
    final hashedPassword = _hashPassword(password);
    
    final request = await client.postUrl(Uri.parse('$baseUrl/auth/login'));
    request.headers.contentType = ContentType.json;
    
    final loginData = {
      'username': username,
      'password': hashedPassword,
    };
    
    request.add(utf8.encode(jsonEncode(loginData)));
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    if (response.statusCode == 200) {
      final data = jsonDecode(responseBody);
      return data['success'] == true;
    } else {
      print('     状态码: ${response.statusCode}');
      print('     响应: $responseBody');
      return false;
    }
  } catch (e) {
    print('     错误: $e');
    return false;
  }
}

/// 密码哈希（与Flutter应用保持一致）
String _hashPassword(String password) {
  final bytes = utf8.encode(password);
  final digest = sha256.convert(bytes);
  return digest.toString();
}
