class ApiConfig {
  // 账号系统服务器URL（开发环境使用本地Mock服务器）
  static const String baseUrl = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api'; // 本地Mock服务器
  static const String wsUrl = 'ws://localhost:3000'; // WebSocket地址

  // 生产环境配置（部署时使用）
  // static const String baseUrl = 'https://your-serverless-api.com';
  // static const String wsUrl = 'wss://your-serverless-ws.com';

  // 原有的小说生成服务器URL
  static const String novelApiUrl = 'https://www.dznovel.top'; // 主域名（更可靠）
  static const String backupUrl = 'http://*************:8000'; // 备用IP地址
  // static const String novelApiUrl = 'http://localhost:8000'; // 开发环境

  // 获取服务器连接超时时间（秒）
  static const int connectionTimeout = 5;

  // API端点配置
  static const Map<String, String> endpoints = {
    // 认证相关
    'sendCode': '/auth/send-code',
    'verifyCode': '/auth/verify-code',
    'register': '/auth/register',
    'login': '/auth/login',
    'logout': '/auth/logout',
    'refreshToken': '/auth/refresh',

    // 用户相关
    'userProfile': '/user/profile',
    'userPassword': '/user/password',
    'userAvatar': '/user/avatar',
    'userSettings': '/user/settings',
    'deleteAccount': '/user/account',

    // 数据同步
    'syncUpload': '/sync/upload',
    'syncDownload': '/sync/download',

    // 支付相关
    'packages': '/packages',
    'createOrder': '/orders/create',
    'myOrders': '/orders/my',
    'cancelOrder': '/orders/{id}/cancel',
    'wechatPay': '/payment/wechat',
    'memberCodePay': '/payment/member-code',
    'paymentStatus': '/payment/status/{id}',

    // 会员码相关
    'validateMemberCode': '/member-code/validate',
  };

  /// 获取API端点URL
  static String getEndpoint(String key, [Map<String, String>? params]) {
    String endpoint = endpoints[key] ?? '';
    if (params != null) {
      params.forEach((key, value) {
        endpoint = endpoint.replaceAll('{$key}', value);
      });
    }
    return '$baseUrl$endpoint';
  }
}
