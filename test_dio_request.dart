import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:crypto/crypto.dart';

// 测试使用Dio发送请求（模拟Flutter应用）
void main() async {
  print('🧪 测试Dio请求（模拟Flutter应用）...');
  print('=' * 50);
  
  try {
    await testDioLogin();
  } catch (e) {
    print('❌ 测试失败: $e');
  }
}

Future<void> testDioLogin() async {
  // 创建Dio实例，配置与Flutter应用相同
  final dio = Dio(BaseOptions(
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
    sendTimeout: const Duration(seconds: 30),
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  ));

  // 添加日志拦截器
  dio.interceptors.add(LogInterceptor(
    requestBody: true,
    responseBody: true,
    requestHeader: true,
    responseHeader: true,
    error: true,
    logPrint: (obj) => print('DIO: $obj'),
  ));

  final baseUrl = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';
  
  print('测试用户: testuser');
  print('原始密码: test123');
  
  final password = 'test123';
  final hashedPassword = _hashPassword(password);
  print('密码哈希: $hashedPassword');
  
  try {
    print('\n发送登录请求...');
    final response = await dio.post(
      '$baseUrl/auth/login',
      data: {
        'username': 'testuser',
        'password': hashedPassword,
      },
    );

    print('\n✅ 登录成功！');
    print('响应数据: ${response.data}');
    
    if (response.data['success'] == true) {
      print('用户: ${response.data['data']['user']['username']}');
      print('Token: ${response.data['data']['token']}');
    }
    
  } catch (e) {
    print('\n❌ 登录失败！');
    print('错误类型: ${e.runtimeType}');
    print('错误详情: $e');
    
    if (e is DioException) {
      print('状态码: ${e.response?.statusCode}');
      print('响应数据: ${e.response?.data}');
      print('响应头: ${e.response?.headers}');
      print('请求选项: ${e.requestOptions}');
      print('请求数据: ${e.requestOptions.data}');
      print('请求头: ${e.requestOptions.headers}');
    }
  }
  
  // 测试其他端点
  print('\n' + '=' * 30);
  print('测试其他端点...');
  
  try {
    print('\n测试套餐获取...');
    final packagesResponse = await dio.get('$baseUrl/packages');
    print('✅ 套餐获取成功');
    print('套餐数量: ${packagesResponse.data['data'].length}');
  } catch (e) {
    print('❌ 套餐获取失败: $e');
  }
  
  try {
    print('\n测试基础连接...');
    final baseResponse = await dio.get(baseUrl);
    print('✅ 基础连接成功');
    print('响应: ${baseResponse.data['message']}');
  } catch (e) {
    print('❌ 基础连接失败: $e');
  }
}

/// 密码哈希（与Flutter应用保持一致）
String _hashPassword(String password) {
  final bytes = utf8.encode(password);
  final digest = sha256.convert(bytes);
  return digest.toString();
}
