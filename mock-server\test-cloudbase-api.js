const axios = require('axios');

// 测试CloudBase API的脚本
// 使用方法: node test-cloudbase-api.js [环境ID]

// 使用固定的环境ID
const ENV_ID = 'novel-app-2gywkgnn15cbd6a8';

// 使用实际的API URL
const BASE_URL = `https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api`;

console.log(`测试CloudBase API: ${BASE_URL}`);
console.log('='.repeat(50));

// 测试用户数据
const testUser = {
  username: 'testuser',
  password: '9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08' // test123的SHA256哈希
};

let authToken = '';

// 测试函数
async function testAPI() {
  try {
    // 1. 测试登录
    console.log('1. 测试用户登录...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, testUser);
    console.log('✅ 登录成功');
    console.log('用户信息:', loginResponse.data.data.user.username);
    authToken = loginResponse.data.data.token;
    
    // 2. 测试获取会员套餐
    console.log('\n2. 测试获取会员套餐...');
    const packagesResponse = await axios.get(`${BASE_URL}/packages`);
    console.log('✅ 获取套餐成功');
    console.log('套餐数量:', packagesResponse.data.data.length);
    
    // 3. 测试验证码发送
    console.log('\n3. 测试发送验证码...');
    const sendCodeResponse = await axios.post(`${BASE_URL}/auth/send-code`, {
      phoneNumber: '13800138000'
    });
    console.log('✅ 发送验证码成功');
    
    // 4. 测试验证码验证
    console.log('\n4. 测试验证码验证...');
    const verifyCodeResponse = await axios.post(`${BASE_URL}/auth/verify-code`, {
      phoneNumber: '13800138000',
      code: '123456'
    });
    console.log('✅ 验证码验证成功');
    
    // 5. 测试会员码验证
    console.log('\n5. 测试会员码验证...');
    try {
      const memberCodeResponse = await axios.post(`${BASE_URL}/member-code/validate`, {
        code: 'VIP2024001'
      });
      console.log('✅ 会员码验证成功');
    } catch (error) {
      console.log('⚠️ 会员码可能已被使用或无效');
    }
    
    // 6. 测试需要认证的接口 - 数据同步下载
    console.log('\n6. 测试数据同步下载...');
    const syncResponse = await axios.get(`${BASE_URL}/sync/download`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    console.log('✅ 数据同步下载成功');
    
    // 7. 测试需要认证的接口 - 数据同步上传
    console.log('\n7. 测试数据同步上传...');
    const uploadResponse = await axios.post(`${BASE_URL}/sync/upload`, {
      data: {
        novels: [],
        characterCards: [],
        userSettings: {}
      }
    }, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    console.log('✅ 数据同步上传成功');
    
    console.log('\n' + '='.repeat(50));
    console.log('🎉 所有API测试通过！');
    console.log('CloudBase部署成功，API正常工作');
    
  } catch (error) {
    console.error('\n❌ API测试失败:');
    if (error.response) {
      console.error('状态码:', error.response.status);
      console.error('错误信息:', error.response.data);
    } else {
      console.error('网络错误:', error.message);
    }
    
    console.log('\n故障排除建议:');
    console.log('1. 检查环境ID是否正确');
    console.log('2. 确认CloudBase函数已成功部署');
    console.log('3. 检查HTTP服务是否已创建');
    console.log('4. 查看CloudBase控制台的函数日志');
  }
}

// 运行测试
testAPI();
