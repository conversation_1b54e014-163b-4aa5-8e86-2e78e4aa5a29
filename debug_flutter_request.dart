import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';

// 调试Flutter应用的确切请求
void main() async {
  print('🔍 调试Flutter应用请求...');
  print('=' * 50);
  
  final client = HttpClient();
  final baseUrl = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';
  
  try {
    // 模拟Flutter应用的确切请求
    await debugLoginRequest(client, baseUrl);
    
  } catch (e) {
    print('❌ 调试失败: $e');
  } finally {
    client.close();
  }
}

Future<void> debugLoginRequest(HttpClient client, String baseUrl) async {
  print('测试用户: testuser');
  print('原始密码: test123');
  
  final password = 'test123';
  final hashedPassword = _hashPassword(password);
  print('密码哈希: $hashedPassword');
  
  // 创建请求数据
  final requestData = {
    'username': 'testuser',
    'password': hashedPassword,
  };
  
  print('\n发送的请求数据:');
  print(jsonEncode(requestData));
  
  // 发送请求
  print('\n发送请求到: $baseUrl/auth/login');
  
  final request = await client.postUrl(Uri.parse('$baseUrl/auth/login'));
  request.headers.contentType = ContentType.json;
  request.headers.set('User-Agent', 'Dart/Flutter');
  
  // 添加所有可能的头部
  request.headers.set('Accept', 'application/json');
  request.headers.set('Content-Type', 'application/json; charset=utf-8');
  
  print('\n请求头部:');
  request.headers.forEach((name, values) {
    print('$name: ${values.join(', ')}');
  });
  
  final requestBody = jsonEncode(requestData);
  print('\n请求体: $requestBody');
  print('请求体长度: ${requestBody.length}');
  
  request.add(utf8.encode(requestBody));
  
  print('\n等待响应...');
  final response = await request.close();
  
  print('响应状态码: ${response.statusCode}');
  print('响应头部:');
  response.headers.forEach((name, values) {
    print('$name: ${values.join(', ')}');
  });
  
  final responseBody = await response.transform(utf8.decoder).join();
  print('\n响应体: $responseBody');
  
  if (response.statusCode == 200) {
    print('\n✅ 登录成功！');
    final data = jsonDecode(responseBody);
    print('用户: ${data['data']['user']['username']}');
  } else {
    print('\n❌ 登录失败！');
    print('状态码: ${response.statusCode}');
    print('错误信息: $responseBody');
    
    // 尝试解析错误响应
    try {
      final errorData = jsonDecode(responseBody);
      print('解析后的错误: ${errorData['message']}');
    } catch (e) {
      print('无法解析错误响应: $e');
    }
  }
  
  // 额外测试：尝试不同的请求格式
  print('\n' + '=' * 30);
  print('尝试不同的请求格式...');
  
  // 测试1: 使用不同的Content-Type
  await testDifferentContentType(client, baseUrl, requestData);
  
  // 测试2: 测试路径变化
  await testDifferentPaths(client, baseUrl, requestData);
}

Future<void> testDifferentContentType(HttpClient client, String baseUrl, Map<String, dynamic> requestData) async {
  print('\n测试不同的Content-Type...');
  
  final request = await client.postUrl(Uri.parse('$baseUrl/auth/login'));
  request.headers.set('Content-Type', 'application/json');
  request.headers.set('Accept', '*/*');
  
  final requestBody = jsonEncode(requestData);
  request.add(utf8.encode(requestBody));
  
  final response = await request.close();
  final responseBody = await response.transform(utf8.decoder).join();
  
  print('状态码: ${response.statusCode}');
  if (response.statusCode != 200) {
    print('错误: $responseBody');
  } else {
    print('✅ 成功');
  }
}

Future<void> testDifferentPaths(HttpClient client, String baseUrl, Map<String, dynamic> requestData) async {
  final paths = ['/auth/login', '/api/auth/login'];
  
  for (final path in paths) {
    print('\n测试路径: $baseUrl$path');
    
    try {
      final request = await client.postUrl(Uri.parse('$baseUrl$path'));
      request.headers.contentType = ContentType.json;
      
      final requestBody = jsonEncode(requestData);
      request.add(utf8.encode(requestBody));
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      print('状态码: ${response.statusCode}');
      if (response.statusCode == 200) {
        print('✅ 路径 $path 成功');
      } else {
        print('❌ 路径 $path 失败: $responseBody');
      }
    } catch (e) {
      print('❌ 路径 $path 异常: $e');
    }
  }
}

/// 密码哈希（与Flutter应用保持一致）
String _hashPassword(String password) {
  final bytes = utf8.encode(password);
  final digest = sha256.convert(bytes);
  return digest.toString();
}
