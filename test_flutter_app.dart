import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';

// 模拟Flutter应用的完整测试
void main() async {
  print('🧪 测试Flutter应用完整流程...');
  print('=' * 60);
  
  final client = HttpClient();
  final baseUrl = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';
  
  try {
    // 1. 测试套餐加载
    print('1. 测试套餐加载...');
    await testPackageLoading(client, baseUrl);
    
    // 2. 测试用户登录
    print('\n2. 测试用户登录...');
    final token = await testUserLogin(client, baseUrl);
    
    // 3. 测试需要认证的接口
    if (token != null) {
      print('\n3. 测试认证接口...');
      await testAuthenticatedAPIs(client, baseUrl, token);
    }
    
    // 4. 测试注册流程
    print('\n4. 测试注册流程...');
    await testRegistrationFlow(client, baseUrl);
    
    print('\n' + '=' * 60);
    print('🎉 Flutter应用测试完成！所有功能正常！');
    
  } catch (e) {
    print('\n❌ 测试失败: $e');
  } finally {
    client.close();
  }
}

Future<void> testPackageLoading(HttpClient client, String baseUrl) async {
  final request = await client.getUrl(Uri.parse('$baseUrl/packages'));
  final response = await request.close();
  final responseBody = await response.transform(utf8.decoder).join();
  
  if (response.statusCode == 200) {
    final data = jsonDecode(responseBody);
    print('   ✅ 套餐加载成功');
    print('   套餐数量: ${data['data'].length}');
    
    // 验证套餐数据结构
    for (final package in data['data']) {
      print('   - ${package['name']}: ¥${package['price']} (${package['durationDays'] == -1 ? '永久' : '${package['durationDays']}天'})');
      
      // 验证必需字段
      final requiredFields = ['id', 'name', 'description', 'price', 'durationDays', 'features', 'limits', 'isActive', 'sortOrder', 'createdAt', 'updatedAt'];
      for (final field in requiredFields) {
        if (!package.containsKey(field)) {
          throw Exception('套餐数据缺少字段: $field');
        }
      }
    }
  } else {
    throw Exception('套餐加载失败: ${response.statusCode}');
  }
}

Future<String?> testUserLogin(HttpClient client, String baseUrl) async {
  final request = await client.postUrl(Uri.parse('$baseUrl/auth/login'));
  request.headers.contentType = ContentType.json;
  
  final loginData = {
    'username': 'testuser',
    'password': _hashPassword('test123'),
  };
  
  request.add(utf8.encode(jsonEncode(loginData)));
  final response = await request.close();
  final responseBody = await response.transform(utf8.decoder).join();
  
  if (response.statusCode == 200) {
    final data = jsonDecode(responseBody);
    print('   ✅ 登录成功');
    print('   用户: ${data['data']['user']['username']}');
    print('   会员状态: ${data['data']['user']['isMember'] ? '会员' : '普通用户'}');
    
    // 验证返回数据结构
    final requiredFields = ['token', 'refreshToken', 'user', 'expiresIn'];
    for (final field in requiredFields) {
      if (!data['data'].containsKey(field)) {
        throw Exception('登录响应缺少字段: $field');
      }
    }
    
    return data['data']['token'];
  } else {
    throw Exception('登录失败: ${response.statusCode} - $responseBody');
  }
}

Future<void> testAuthenticatedAPIs(HttpClient client, String baseUrl, String token) async {
  // 测试数据同步下载
  print('   测试数据同步下载...');
  final syncRequest = await client.getUrl(Uri.parse('$baseUrl/sync/download'));
  syncRequest.headers.set('Authorization', 'Bearer $token');
  final syncResponse = await syncRequest.close();
  final syncBody = await syncResponse.transform(utf8.decoder).join();
  
  if (syncResponse.statusCode == 200) {
    final syncData = jsonDecode(syncBody);
    print('   ✅ 数据同步下载成功');
    print('   同步数据包含: ${syncData['data'].keys.join(', ')}');
  } else {
    print('   ⚠️ 数据同步下载失败: ${syncResponse.statusCode}');
  }
  
  // 测试数据同步上传
  print('   测试数据同步上传...');
  final uploadRequest = await client.postUrl(Uri.parse('$baseUrl/sync/upload'));
  uploadRequest.headers.contentType = ContentType.json;
  uploadRequest.headers.set('Authorization', 'Bearer $token');
  
  final uploadData = {
    'data': {
      'novels': [],
      'characterCards': [],
      'userSettings': {'theme': 'dark'}
    }
  };
  
  uploadRequest.add(utf8.encode(jsonEncode(uploadData)));
  final uploadResponse = await uploadRequest.close();
  
  if (uploadResponse.statusCode == 200) {
    print('   ✅ 数据同步上传成功');
  } else {
    print('   ⚠️ 数据同步上传失败: ${uploadResponse.statusCode}');
  }
}

Future<void> testRegistrationFlow(HttpClient client, String baseUrl) async {
  // 1. 发送验证码
  print('   发送验证码...');
  final codeRequest = await client.postUrl(Uri.parse('$baseUrl/auth/send-code'));
  codeRequest.headers.contentType = ContentType.json;
  
  final codeData = {'phoneNumber': '13900139001'};
  codeRequest.add(utf8.encode(jsonEncode(codeData)));
  final codeResponse = await codeRequest.close();
  
  if (codeResponse.statusCode == 200) {
    print('   ✅ 验证码发送成功');
  } else {
    print('   ⚠️ 验证码发送失败');
    return;
  }
  
  // 2. 验证验证码
  print('   验证验证码...');
  final verifyRequest = await client.postUrl(Uri.parse('$baseUrl/auth/verify-code'));
  verifyRequest.headers.contentType = ContentType.json;
  
  final verifyData = {'phoneNumber': '13900139001', 'code': '123456'};
  verifyRequest.add(utf8.encode(jsonEncode(verifyData)));
  final verifyResponse = await verifyRequest.close();
  
  if (verifyResponse.statusCode == 200) {
    print('   ✅ 验证码验证成功');
  } else {
    print('   ⚠️ 验证码验证失败');
  }
  
  // 3. 验证会员码
  print('   验证会员码...');
  final memberRequest = await client.postUrl(Uri.parse('$baseUrl/member-code/validate'));
  memberRequest.headers.contentType = ContentType.json;
  
  final memberData = {'code': 'VIP2024001'};
  memberRequest.add(utf8.encode(jsonEncode(memberData)));
  final memberResponse = await memberRequest.close();
  
  if (memberResponse.statusCode == 200) {
    print('   ✅ 会员码验证成功');
  } else {
    print('   ⚠️ 会员码验证失败（可能已被使用）');
  }
}

/// 密码哈希（与Flutter应用保持一致）
String _hashPassword(String password) {
  final bytes = utf8.encode(password);
  final digest = sha256.convert(bytes);
  return digest.toString();
}
