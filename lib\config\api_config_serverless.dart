import 'package:flutter/foundation.dart';

/// API配置类 - 腾讯云Serverless版本
/// 
/// 将此文件替换原有的api_config.dart文件，或者合并配置
class ApiConfig {
  /// 生产环境API地址（替换为实际的API网关地址）
  static const String baseUrl = 'https://service-xxxxxx-1234567890.gz.apigw.tencentcs.com';
  
  /// 开发环境API地址
  static const String devBaseUrl = 'http://localhost:3000';
  
  /// 测试环境API地址
  static const String testBaseUrl = 'https://test-api.example.com';
  
  /// 当前环境API地址
  static String get currentBaseUrl {
    if (kReleaseMode) {
      return baseUrl; // 生产环境
    } else if (const bool.fromEnvironment('dart.vm.product')) {
      return testBaseUrl; // 测试环境
    } else {
      return devBaseUrl; // 开发环境
    }
  }
  
  /// API超时时间
  static const Duration timeout = Duration(seconds: 30);
  
  /// 请求重试次数
  static const int retryCount = 3;
  
  /// 是否启用日志
  static bool get enableLogging => !kReleaseMode;
  
  /// 是否启用缓存
  static const bool enableCache = true;
  
  /// 缓存过期时间
  static const Duration cacheExpiration = Duration(minutes: 5);
  
  /// 是否启用请求压缩
  static const bool enableCompression = true;
  
  /// 是否启用请求重试
  static const bool enableRetry = true;
  
  /// 是否启用离线模式
  static const bool enableOfflineMode = true;
  
  /// 环境检测
  static bool get isProduction => kReleaseMode;
  static bool get isDevelopment => !kReleaseMode && !kProfileMode;
  static bool get isProfile => kProfileMode;

  /// API端点配置
  static const Map<String, String> endpoints = {
    'auth': '/auth',
    'user': '/user',
    'payment': '/payment',
    'packages': '/packages',
    'sync': '/sync',
    'orders': '/orders',
    'member-code': '/member-code',
  };

  /// 获取完整的API地址
  static String getApiUrl(String endpoint) {
    return '$currentBaseUrl${endpoints[endpoint] ?? endpoint}';
  }

  /// 请求头配置
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': 'NovelApp/1.0.0',
  };

  /// 认证头配置
  static Map<String, String> getAuthHeaders(String token) => {
    ...defaultHeaders,
    'Authorization': 'Bearer $token',
  };

  /// 错误处理配置
  static const Map<int, String> errorMessages = {
    400: '请求参数错误',
    401: '未授权访问',
    403: '权限不足',
    404: '资源不存在',
    429: '请求过于频繁',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务不可用',
    504: '网关超时',
  };

  /// 获取错误信息
  static String getErrorMessage(int statusCode) {
    return errorMessages[statusCode] ?? '未知错误';
  }

  /// 调试配置
  static void printDebugInfo() {
    if (enableLogging) {
      print('=== API配置信息 ===');
      print('当前环境: ${isProduction ? "生产" : isDevelopment ? "开发" : "测试"}');
      print('API地址: $currentBaseUrl');
      print('超时时间: ${timeout.inSeconds}秒');
      print('重试次数: $retryCount');
      print('启用缓存: $enableCache');
      print('==================');
    }
  }
}
