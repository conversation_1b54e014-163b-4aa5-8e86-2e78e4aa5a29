// 简单的测试版本
exports.main = async (event, context) => {
  console.log('=== CloudBase Function Called ===');
  console.log('Event:', JSON.stringify(event, null, 2));
  console.log('Context:', JSON.stringify(context, null, 2));

  try {
    // 处理OPTIONS请求
    if (event.httpMethod === 'OPTIONS') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type,Authorization'
        },
        body: ''
      };
    }

    // 简单的路由处理
    const path = event.path || '/';
    const method = event.httpMethod || 'GET';

    console.log(`Processing ${method} ${path}`);

    // 基本的API响应
    if (path === '/api' || path === '/') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          success: true,
          message: 'Novel App API is running on CloudBase',
          timestamp: new Date().toISOString(),
          path: path,
          method: method
        })
      };
    }

    // 登录接口
    if ((path === '/api/auth/login' || path === '/auth/login') && method === 'POST') {
      let body;
      try {
        body = event.body ? (typeof event.body === 'string' ? JSON.parse(event.body) : event.body) : {};
      } catch (e) {
        console.log('Failed to parse request body:', e);
        console.log('Raw body:', event.body);
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '请求数据格式错误'
          })
        };
      }

      console.log('Login request body:', body);
      console.log('Username:', body.username);
      console.log('Password hash:', body.password);
      console.log('Request path:', path);
      console.log('Request method:', method);

      // 支持的测试用户和密码哈希
      const validUsers = {
        'testuser': {
          // test123 的 SHA256 哈希
          passwordHash: 'ecd71870d1963316a97e3ac3408c9835ad8cf0f3c1bc703527c30265534f75ae',
          user: {
            id: 'test-user-id',
            username: 'testuser',
            phoneNumber: '13800138000',
            email: null,
            avatar: null,
            isMember: false,
            memberExpireTime: null,
            membershipType: 'none',
            isPermanentMember: false,
            isDataSyncEnabled: true,
            settings: {
              enableBiometric: false,
              autoSync: true,
              enableNotification: true,
              theme: 'system',
              language: 'zh-CN'
            }
          }
        },
        'admin': {
          // admin123 的 SHA256 哈希
          passwordHash: '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9',
          user: {
            id: 'admin-user-id',
            username: 'admin',
            phoneNumber: '13900139000',
            email: '<EMAIL>',
            avatar: null,
            isMember: true,
            memberExpireTime: null,
            membershipType: 'permanent',
            isPermanentMember: true,
            isDataSyncEnabled: true,
            settings: {
              enableBiometric: false,
              autoSync: true,
              enableNotification: true,
              theme: 'system',
              language: 'zh-CN'
            }
          }
        }
      };

      const userInfo = validUsers[body.username];
      if (userInfo && body.password === userInfo.passwordHash) {
        console.log('Login successful for user:', body.username);
        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              token: `token-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              refreshToken: `refresh-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              user: userInfo.user,
              expiresIn: 86400
            }
          })
        };
      } else {
        console.log('Login failed - invalid credentials');
        console.log('Expected hash for testuser:', validUsers.testuser?.passwordHash);
        console.log('Received hash:', body.password);
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '用户名或密码错误'
          })
        };
      }
    }

    // 获取套餐接口
    if ((path === '/api/packages' || path === '/packages') && method === 'GET') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          success: true,
          data: [
            {
              id: 'monthly',
              name: '月度会员',
              description: '享受一个月的会员特权，包含所有高级功能',
              price: 18.88,
              durationDays: 30,
              features: [
                '无限制章节生成',
                '高级AI模型访问',
                '多格式导出',
                '扩展功能使用',
                '优先客服支持'
              ],
              limits: {
                maxChaptersPerNovel: -1,
                maxKnowledgeDocuments: 100,
                canUseExtendedFeatures: true,
                maxNovelsPerDay: -1,
                maxWordsPerGeneration: -1,
                canExportToMultipleFormats: true,
                canUseAdvancedAI: true,
                maxCustomCharacterTypes: -1
              },
              isActive: true,
              sortOrder: 1,
              createdAt: '2024-01-01T00:00:00.000Z',
              updatedAt: '2024-01-01T00:00:00.000Z'
            },
            {
              id: 'permanent',
              name: '永久会员',
              description: '一次购买，终身享受所有会员特权',
              price: 188.88,
              durationDays: -1,
              features: [
                '永久无限制章节生成',
                '永久高级AI模型访问',
                '永久多格式导出',
                '永久扩展功能使用',
                '永久优先客服支持',
                '未来新功能免费使用'
              ],
              limits: {
                maxChaptersPerNovel: -1,
                maxKnowledgeDocuments: -1,
                canUseExtendedFeatures: true,
                maxNovelsPerDay: -1,
                maxWordsPerGeneration: -1,
                canExportToMultipleFormats: true,
                canUseAdvancedAI: true,
                maxCustomCharacterTypes: -1
              },
              isActive: true,
              sortOrder: 2,
              createdAt: '2024-01-01T00:00:00.000Z',
              updatedAt: '2024-01-01T00:00:00.000Z'
            }
          ]
        })
      };
    }

    // 发送验证码接口
    if ((path === '/api/auth/send-code' || path === '/auth/send-code') && method === 'POST') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          success: true,
          message: '验证码发送成功'
        })
      };
    }

    // 验证验证码接口
    if ((path === '/api/auth/verify-code' || path === '/auth/verify-code') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      if (body.code === '123456') {
        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '验证码验证成功'
          })
        };
      } else {
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '验证码错误'
          })
        };
      }
    }

    // 验证会员码接口
    if ((path === '/api/member-code/validate' || path === '/member-code/validate') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      if (body.code === 'VIP2024001') {
        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '会员码有效'
          })
        };
      } else {
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '会员码无效或已过期'
          })
        };
      }
    }

    // 数据同步下载接口
    if ((path === '/api/sync/download' || path === '/sync/download') && method === 'GET') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          success: true,
          data: {
            novels: [],
            characterCards: [],
            characterTypes: [],
            knowledgeDocuments: [],
            stylePackages: [],
            userSettings: {
              enableBiometric: false,
              autoSync: true,
              enableNotification: true,
              theme: 'system',
              language: 'zh-CN'
            }
          }
        })
      };
    }

    // 数据同步上传接口
    if ((path === '/api/sync/upload' || path === '/sync/upload') && method === 'POST') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          success: true,
          message: '数据同步成功'
        })
      };
    }

    // 默认404响应
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: false,
        message: 'API endpoint not found',
        path: path,
        method: method
      })
    };

  } catch (error) {
    console.error('Function error:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};
